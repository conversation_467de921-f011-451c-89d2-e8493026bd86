stages:
  - pipeline
  - lint
  - test
  - build
  - deploy
  - deploy_dev
  - deploy_prod

variables:
  POETRY_HTTP_BASIC_INDIEBI_USERNAME: "gitlab-ci-token"
  POETRY_HTTP_BASIC_INDIEBI_PASSWORD: "$CI_JOB_TOKEN"

include:
  - local: common/job-pipeline.yml
    inputs:
      job-dir: "pbi_refresh"
  - local: common/job-pipeline.yml
    inputs:
      job-dir: "find_shards"
  - local: common/job-pipeline.yml
    inputs:
      job-dir: "update_shared"
  - local: common/job-pipeline.yml
    inputs:
      job-dir: "saas_gold"
      python-version: "3.11"
  - local: common/job-pipeline.yml
    inputs:
      job-dir: "ppt_gold"
      python-version: "3.11"
  - local: common/job-pipeline.yml
    inputs:
      job-dir: "core_silver"
      python-version: "3.11"
      force-project-dir: "$CI_PROJECT_DIR"
      job-dockerfile: "common/gold_job.Dockerfile"
      extra-test-command: "poetry run poe ci-slow-test"
  - local: common/job-pipeline.yml
    inputs:
      job-dir: "events_service_gold"
      python-version: "3.11"
  - local: common/job-pipeline.yml
    inputs:
      job-dir: "direct_data_access_gold"
      python-version: "3.11"
  - local: common/job-pipeline.yml
    inputs:
      job-dir: "processing_notification"
      python-version: "3.11"

  - local: common/lib-pipeline.yml
    inputs:
      lib-dir: "data-sdk"

.deploy_pipeline:
  stage: pipeline
  image: registry.gitlab.com/bluebrick/indiebi/infra/python-ci-image:python-3.10
  script:
    - az login --identity
    - export API_KEY=$(az keyvault secret show --vault-name $KEY_VAULT_NAME --name pipeline-manager-api-key --query value --output tsv)
    - echo ${PIPELINE_MANAGER_URL} uploading ${PIPELINE_FILE}
    - 'curl -X PUT --fail -H "x-api-key: ${API_KEY}" -H "Content-Type: application/json" -d @${PIPELINE_FILE} ${PIPELINE_MANAGER_URL}/pipeline-definition/full-definition'

pipeline.json:deploy_dev:
  extends: .deploy_pipeline
  variables:
    PIPELINE_FILE: "pipeline.json"
    KEY_VAULT_NAME: "kv-single-click-dev-b0"
    PIPELINE_MANAGER_URL: "https://pipeline-manager.indiebi.dev"
  rules:
    - if: $CI_MERGE_REQUEST_ID
      when: manual
      allow_failure: false
      changes:
        - "pipeline.json"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
      allow_failure: false
      changes:
        - "pipeline.json"

pipeline.json:deploy_prod:
  extends: .deploy_pipeline
  variables:
    PIPELINE_FILE: "pipeline.json"
    KEY_VAULT_NAME: "kv-single-click-prod-s8"
    PIPELINE_MANAGER_URL: "https://pipeline-manager.indiebi.com"
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
      allow_failure: false
      changes:
        - "pipeline.json"

pipeline_data_sharing.json:deploy_dev:
  extends: .deploy_pipeline
  variables:
    PIPELINE_FILE: "pipeline_data_sharing.json"
    KEY_VAULT_NAME: "kv-single-click-dev-b0"
    PIPELINE_MANAGER_URL: "https://pipeline-manager.indiebi.dev"
  rules:
    - if: $CI_MERGE_REQUEST_ID
      when: manual
      allow_failure: false
      changes:
        - "pipeline_data_sharing.json"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
      allow_failure: false
      changes:
        - "pipeline_data_sharing.json"

pipeline_data_sharing.json:deploy_prod:
  extends: .deploy_pipeline
  variables:
    PIPELINE_FILE: "pipeline_data_sharing.json"
    KEY_VAULT_NAME: "kv-single-click-prod-s8"
    PIPELINE_MANAGER_URL: "https://pipeline-manager.indiebi.com"
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
      allow_failure: false
      changes:
        - "pipeline_data_sharing.json"

default:
  tags:
    - dpt-azure
