import datetime
import logging
import traceback

from pydantic import BaseModel, ValidationError

from data_sdk.aggregator import BaseAggregator, process_aggregators
from data_sdk.custom_partition.reader import CustomPartitionReader
from data_sdk.custom_partition.writer import CustomPartitionsWriter
from data_sdk.domain.domain_types import StudioId
from saas_gold.aggregators import (
    DimPortalsAggregator,
    DimProductsAggregator,
    DimSkuAggregator,
    DimStudioAggregator,
    DimTrafficSourceAggregator,
    ExportFactSalesAggregator,
    ExportFactVisibilityWishlistAggregator,
    FactBaselineAggregator,
    FactEventDayAggregator,
    FactEventsAggregator,
    FactRetailerTagsAggregator,
    FactSalesAggregator,
    FactSalesCumulativeAggregator,
    FactSalesIndicatorAggregator,
    FactVisibilityAggregator,
    FactWishlistActionsAggregator,
    FactWishlistCohortsAggregator,
)
from saas_gold.config import Config
from saas_gold.exceptions import InvalidMessage
from saas_gold.writers.chained_writer import ChainedWriter

log = logging.getLogger(__name__)


class Params(BaseModel):
    studio_id: int


def validate_message(params) -> Params:
    log.info(f"Validating message: {params}")
    try:
        return Params.validate(params)
    except ValidationError:
        log.exception("Received invalid message %s", str(params))
        raise InvalidMessage(traceback.format_exc())


aggregators: list[type[BaseAggregator]] = [
    DimPortalsAggregator,
    DimSkuAggregator,
    DimProductsAggregator,
    DimStudioAggregator,
    DimTrafficSourceAggregator,
    ExportFactSalesAggregator,
    ExportFactVisibilityWishlistAggregator,
    FactSalesAggregator,
    FactSalesCumulativeAggregator,
    FactSalesIndicatorAggregator,
    FactVisibilityAggregator,
    FactWishlistActionsAggregator,
    FactWishlistCohortsAggregator,
    FactEventDayAggregator,
    FactEventsAggregator,
    FactBaselineAggregator,
    FactRetailerTagsAggregator,
]

prod_export_studios = [
    9,
    253,
    258,
    275,
    551,
    611,
    703,
    705,
    1007,
    1024,
    1064,
    1144,
    1267,
    1480,
    10121,
    10913,
    10940,
    10942,
    10943,
    10944,
    10946,
    11062,
]
dev_export_studios = [1]


def run(params: Params, config: Config):
    pbi_result_writer = CustomPartitionsWriter.get_writer(config.pbi_output_cfg)
    csv_export_writer = None
    if (
        (config.env == "prod" and params.studio_id in prod_export_studios)
        or (config.env == "dev" and params.studio_id in dev_export_studios)
        or (config.env == "local")
    ):
        csv_export_writer = CustomPartitionsWriter.get_writer(
            config.direct_data_access_cfg
        )
    chained_writer = ChainedWriter(
        parquet_writer=pbi_result_writer, csv_writer=csv_export_writer
    )

    result_reader = CustomPartitionReader.get_reader(config.input_cfg)

    creation_datetime = datetime.datetime.now(datetime.UTC)

    process_aggregators(
        aggregators=aggregators,
        reader=result_reader,
        writer=chained_writer,
        creation_datetime=creation_datetime,
        studio_id=StudioId(params.studio_id),
    )
