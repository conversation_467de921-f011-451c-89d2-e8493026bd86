from data_sdk.custom_partition.writer import CustomPartitionsWriter
from data_sdk.domain.tables import TableDefinition
from data_sdk.segmentator import SegmentDefinition
from saas_gold.segmentators.single_file_no_timestamp import (
    SingleFileNoTimestampSegmentator,
)


class ChainedWriter(CustomPartitionsWriter):
    def __init__(
        self,
        parquet_writer: CustomPartitionsWriter,
        csv_writer: CustomPartitionsWriter | None,
    ) -> None:
        self._parquet_writer = parquet_writer
        self._csv_writer = csv_writer

    def save_table(
        self, table: TableDefinition, partition: str, segments: list[SegmentDefinition]
    ):
        self._parquet_writer.save_table(table, partition, segments)
        # Save CSV
        if self._csv_writer:
            csv_segments = SingleFileNoTimestampSegmentator().create_segments(
                table, None
            )
            # TODO: dirty hack until refactor :) it assumes that path to Parquet is studio/table and I truncat till studio.
            csv_partition = partition.split("/")[0]
            self._csv_writer.save_table(table, csv_partition, csv_segments)

    def close(self):
        self._parquet_writer.close()
        if self._csv_writer:
            self._csv_writer.close()
