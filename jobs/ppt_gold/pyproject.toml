[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poe]
include = "../../common/poe_shared_tasks.toml"

[tool.poe.tasks]
hooks = { "shell" = "../../.hooks/install.sh 'jobs/ppt_gold'" }
_test = "pytest -vvv --failed-first --random-order --color=yes"

[tool.poetry]
name = "ppt_gold"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
pandas = "^2.2.0"
pydantic-settings = "^2.1.0"
pydantic = "^2.6.1"
polars = "^0.20.10"
pipeline-sdk = {version = "^2.4.0", source = "indiebi"}
data-sdk = {version = "1.5.1", source = "indiebi"}

typer = { extras = ["all"], version = "^0.12.0" }
scipy = "^1.12.0"
sentry-sdk = "^2.0.0"
azure-storage-blob = "^12.19.1"
events-calculator = { version = "0.5.10", source = "indiebi" }

[tool.poetry.group.dev.dependencies]
ruff = "^0.4.5"
mypy = "^1.8.0"
pandas-stubs = "^2.1.4.231227"
pytest-random-order = "^1.1.1"
poethepoet = "^0.26.1"
pre-commit = "^3.7.1"

[tool.poetry.group.test.dependencies]
pytest = "^8.2.2"
pytest-deadfixtures = "^2.2.1"
pytest-xdist = "^3.6.1"
pytest-random-order = "^1.1.1"

[[tool.poetry.source]]
name = "indiebi"
url = "https://gitlab.com/api/v4/groups/4449864/-/packages/pypi/simple"
priority = "explicit"

[tool.pytest.ini_options]
log_cli = true
log_cli_level = "INFO"
log_cli_format = "%(message)s"

log_file = "pytest.log"
log_file_level = "DEBUG"
log_file_format = "%(asctime)s [%(levelname)8s] %(message)s (%(filename)s:%(lineno)s)"
log_file_date_format = "%Y-%m-%d %H:%M:%S"

[tool.ruff]
src = [".", "tests"]

[tool.ruff.lint]
select = [
    # Pyflakes
    "F",
    # Pycodestyle
    "E",
    "W",
    # isort
    "I",
]
ignore = ["E501", "E712"]


[tool.ruff.lint.isort]
known-first-party = ["ppt_gold"]

[tool.ruff.lint.per-file-ignores]
"ppt_gold/main.py" = ["E402"]
