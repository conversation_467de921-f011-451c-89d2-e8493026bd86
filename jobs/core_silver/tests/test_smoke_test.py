import contextlib
import shutil
from pathlib import Path

import polars as pl
import pytest

from core_silver import get_project_root
from core_silver.config import Config
from core_silver.job import JobInputParameters, run
from data_sdk.config import LocalConfig
from data_sdk.domain import ALL_PORTALS, Portal, TableName
from data_sdk.domain.domain_types import ReportMetadata, StudioId
from data_sdk.domain.source import Source, source_to_observation_type, source_to_portal

EXCLUDED_SOURCES = {
    # These test cases are not working yet
    Source.STEAM_WISHLIST_COUNTRY,  # Replaced by STEAM_WISHLIST_BALANCE
}


SUPPORTED_SOURCES = [source for source in Source if source not in EXCLUDED_SOURCES]

EXCLUDED_PORTALS = {}

DEFAULT_SUPPORTED_PORTALS = [
    portal for portal in ALL_PORTALS if portal not in EXCLUDED_PORTALS
]

EXCLUDED_TABLES = {
    TableName.DIM_ACQUISITION_PROPERTIES,  # This table has been removed from CS
    TableName.DIM_SHARED,  # This table has been removed from CS
    TableName.DIM_STUDIO,  # This table has been replaced by EXTERNAL_STUDIO
    TableName.DIM_TRAFFIC_SOURCE,  # This table has been replaced by SILVER_TRAFFIC_SOURCE
    TableName.FACT_DATES_COVERAGE,  # This table has been removed from CS
    TableName.FACT_DISCOUNTS,  # Replace by OBSERVATION_DISCOUNTS
    TableName.FACT_DISCOUNT_DEPTH_INPUT,  # Moved to PPT GoOLD
    TableName.SHARED,  # This table has been removed from CS
    TableName.SKU_EVENTS_PLANNER,  # This table has not been migrated yet
    TableName.EXTERNAL_CURRENCY_EXCHANGE_RATES,  # This table is not partitioned by portal
    TableName.EXTERNAL_FEATURE_FLAGS,  # This table is not partitioned by portal
}
SUPPORTED_TABLES = list(set(TableName) - EXCLUDED_TABLES)

DISCOUNT_PORTALS = [Portal.NINTENDO, Portal.STEAM]
EXTERNAL_PORTALS = [None]  # External tables are not partitioned by portal
VISIBILITY_PORTALS = [Portal.STEAM]
WISHLIST_ACTIONS_PORTALS = [Portal.STEAM, Portal.PLAYSTATION, Portal.NINTENDO]
WISHLIST_COHORTS_PORTALS = [Portal.STEAM]
CUMULATIVE_WISHLIST_SALES = [Portal.NINTENDO]
WISHLIST_BALANCE = [Portal.STEAM]
DAILY_ACTIVE_USERS = [Portal.STEAM]

SUPPORTED_TABLES_AND_PORTALS = {
    table: (
        EXTERNAL_PORTALS
        if table.name.startswith("EXTERNAL")
        else DEFAULT_SUPPORTED_PORTALS
    )
    for table in SUPPORTED_TABLES
}
SUPPORTED_TABLES_AND_PORTALS.update({
    TableName.OBSERVATION_DISCOUNTS: DISCOUNT_PORTALS,
    TableName.OBSERVATION_VISIBILITY: VISIBILITY_PORTALS,
    TableName.OBSERVATION_WISHLIST_ACTIONS: WISHLIST_ACTIONS_PORTALS,
    TableName.OBSERVATION_WISHLIST_COHORTS: WISHLIST_COHORTS_PORTALS,
    TableName.SILVER_TRAFFIC_SOURCE: VISIBILITY_PORTALS,
    TableName.FACT_DETECTED_EVENTS: DISCOUNT_PORTALS,
    TableName.FACT_EVENT_DAY: DISCOUNT_PORTALS,
    TableName.FACT_VISIBILITY: VISIBILITY_PORTALS,
    TableName.FACT_WISHLIST_ACTIONS: WISHLIST_ACTIONS_PORTALS,
    TableName.FACT_WISHLIST_COHORTS: WISHLIST_COHORTS_PORTALS,
    # FACT_BASELINE is calculated only for portals with enough data in mock files!
    # Empty parquets for other portals are not written.
    TableName.FACT_BASELINE: [
        Portal.APPLE,
        Portal.GOOGLE,
        Portal.META,
        Portal.NINTENDO,
        Portal.STEAM,
    ],
    TableName.OBSERVATION_CUMULATIVE_WISHLIST_SALES: CUMULATIVE_WISHLIST_SALES,
    TableName.OBSERVATION_WISHLIST_BALANCE: WISHLIST_BALANCE,
    TableName.OBSERVATION_DAILY_ACTIVE_USERS: DAILY_ACTIVE_USERS,
})


def get_files(path: Path):
    return sorted([str(x.relative_to(path)) for x in path.glob("**/*") if x.is_file()])


def get_directories(path: Path):
    return sorted([str(x.relative_to(path)) for x in path.glob("*") if x.is_dir()])


@pytest.fixture
def studio_id():
    return StudioId(1)


@pytest.fixture
def output_directory():
    # TODO change to tmp
    output_path = get_project_root() / "smoke_test"
    with contextlib.suppress(FileNotFoundError):
        shutil.rmtree(output_path)
    output_path.mkdir()
    return get_project_root() / "smoke_test"


@pytest.fixture
def converted_directory(output_directory):
    return output_directory / "converted"


@pytest.fixture
def result_directory(output_directory):
    return output_directory / "result"


@pytest.fixture
def get_result_portals(result_directory, studio_id):
    def _get_result_portals(table: TableName) -> list[Portal | None]:
        base_path = result_directory / table.value / f"studio_id={studio_id}"
        directories = get_directories(base_path)
        if not directories:
            return []
        if directories[0].startswith("version"):
            return [None]
        else:
            return [Portal[d.split("=")[1].upper()] for d in directories]

    return _get_result_portals


@pytest.fixture
def get_latest_result(result_directory, studio_id):
    def _get_latest_result(
        table: TableName, portal: Portal | None
    ) -> list[Path] | Path | None:
        base_path = result_directory / table.value / f"studio_id={studio_id}"
        if portal is not None:
            base_path = base_path / f"portal={portal.value}"
        base_path = base_path / "version=v1"
        filenames = get_files(base_path)
        if not filenames:
            return None
        else:
            return base_path / max(filenames)

    return _get_latest_result


@pytest.fixture
def get_reports_metadata(get_latest_result):
    def _get_reports_metadata() -> list[ReportMetadata]:
        unique_sources = set(SUPPORTED_SOURCES)
        latest_report_path = get_latest_result(TableName.EXTERNAL_REPORTS, portal=None)
        reports = pl.read_parquet(
            latest_report_path, hive_partitioning=False
        ).to_dicts()
        return [
            ReportMetadata(**report_data)
            for report_data in reports
            if report_data["source"] in unique_sources
        ]

    return _get_reports_metadata


@pytest.fixture
def smoke_test_config(output_directory, studio_id):
    return Config(
        input_cfg=LocalConfig(
            # TODO change to read from playground
            local_dir=get_project_root() / "tests" / "snapshot" / "input"
        ),
        output_cfg=LocalConfig(local_dir=output_directory / "result"),
        converted_reports_cfg=LocalConfig(local_dir=output_directory / "converted"),
    )


@pytest.fixture
def get_converted_files(converted_directory, studio_id):
    def _get_convert_files():
        return get_files(converted_directory / f"studio_id={studio_id}")

    return _get_convert_files


@pytest.fixture
def get_converted_report(converted_directory, studio_id):
    def _get_converted_report(report: ReportMetadata):
        return pl.read_parquet(
            converted_directory / f"studio_id={studio_id}" / report.converted_filename,
            hive_partitioning=False,
        )

    return _get_converted_report


def test_smoke_test(
    smoke_test_config,
    studio_id,
    get_converted_report,
    get_converted_files,
    get_reports_metadata,
    get_result_portals,
    get_latest_result,
):
    """This test processes all sources in a sequence, retaining the files from the
    previous runs. It enables comprehensive tests of silver and aggregated tables.

    The report list is read from StaticReportServiceClient."""
    # Run pytest with --log-cli-level=info to see the logs

    for source in SUPPORTED_SOURCES:
        observation_type = source_to_observation_type[source]
        portal = source_to_portal[source]
        run(
            params=JobInputParameters(
                studio_id=StudioId(studio_id),
                observation_type=observation_type,
                portal=portal,
            ),
            config=smoke_test_config,
        )

    converted_reports_metadata = get_reports_metadata()
    assert len(converted_reports_metadata) == len(
        SUPPORTED_SOURCES
    ), "All supported sources should be should be present in the test"

    converted_filenames = get_converted_files()
    assert set(converted_filenames) == {
        report.converted_filename for report in converted_reports_metadata
    }, "All files should be converted"

    for report in converted_reports_metadata:
        df = get_converted_report(report)
        assert (
            df.is_empty() is False
        ), f"Converted file {report.converted_filename} for report {report} should not be empty"

    for table, expected_portals in SUPPORTED_TABLES_AND_PORTALS.items():
        actual_portals = get_result_portals(table)

        assert set(actual_portals) == set(
            expected_portals
        ), f"Expected and actual portals for {table} don't match"

        for portal in actual_portals:
            aggregated_table_path = get_latest_result(table, portal)
            if (
                aggregated_table_path is None
            ):  # TODO: fix get_latest_result so this would not have to be checked
                continue
            df = pl.read_parquet(
                aggregated_table_path,
                hive_partitioning=False,
            )
            assert (
                df.is_empty() is False
            ), f"Aggregated table f{table} for portal {portal} should not be empty - {aggregated_table_path}"
